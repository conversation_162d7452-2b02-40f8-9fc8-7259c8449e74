import os
import subprocess
import shutil

# --- Configuration ---
# NOTE: These paths are for WSL. If you run this on Windows, you'll need to change them.
# For example, 'E:\Maildir\cur' on Windows becomes '/mnt/e/Maildir/cur' in WSL.

SOURCE_DIR = "/mnt/e/Maildir/cur"
SPAM_DIR = "/mnt/e/Maildir/rez/spam"
HAM_DIR = "/mnt/e/Maildir/rez/ham"
SPAM_LOG_FILE = os.path.join(SPAM_DIR, "spam_scores.txt")

# --- Helper Functions ---

def get_next_filename(directory):
    """Calculates the next sequential filename (e.g., '1', '2', '3')."""
    return str(len(os.listdir(directory)) + 1)

def check_spam(file_path):
    """
    Checks if an email file is spam using SpamAssassin.
    Returns a tuple: (is_spam, score)
    """
    try:
        # Run spamassassin and capture the output and exit code
        result = subprocess.run(['spamassassin', '-t'], stdin=open(file_path, 'r'), capture_output=True, text=True)
        
        # SpamAssassin adds an 'X-Spam-Status' header. We check if it's "Yes".
        is_spam = "X-Spam-Status: Yes" in result.stdout

        # Extract the score
        score = "0.0"
        if is_spam:
            for line in result.stdout.splitlines():
                if line.startswith("X-Spam-Status:"):
                    # Example line: X-Spam-Status: Yes, score=12.3 required=5.0 ...
                    parts = line.split(' ')
                    for part in parts:
                        if part.startswith("score="):
                            score = part.split('=')[1]
                            break
                    break
        return is_spam, score
    except FileNotFoundError:
        print("Error: 'spamassassin' command not found. Is it installed and in your PATH?")
        return None, None
    except Exception as e:
        print(f"An error occurred while checking {file_path}: {e}")
        return None, None

def process_emails():
    """
    Main function to process all emails in the source directory.
    """
    # Ensure destination directories exist
    os.makedirs(SPAM_DIR, exist_ok=True)
    os.makedirs(HAM_DIR, exist_ok=True)

    print(f"Scanning for email files in {SOURCE_DIR}...")

    for filename in os.listdir(SOURCE_DIR):
        # Skip directories and hidden files
        source_path = os.path.join(SOURCE_DIR, filename)
        if not os.path.isfile(source_path) or filename.startswith('.'):
            continue

        source_path = os.path.join(SOURCE_DIR, filename)
        print(f"Processing {source_path}...")

        is_spam, score = check_spam(source_path)

        if is_spam is None:
            # An error occurred in check_spam
            continue

        if is_spam:
            # --- Handle Spam ---
            new_name = get_next_filename(SPAM_DIR)
            dest_path = os.path.join(SPAM_DIR, new_name)
            
            print(f"  -> SPAM (Score: {score}). Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)
            
            # Log the spam score
            with open(SPAM_LOG_FILE, 'a') as log_file:
                log_file.write(f"{new_name}: {score}\n")

        else:
            # --- Handle Ham ---
            new_name = get_next_filename(HAM_DIR)
            dest_path = os.path.join(HAM_DIR, new_name)

            print(f"  -> HAM. Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)

    print("\nProcessing complete.")

# --- Main Execution ---
if __name__ == "__main__":
    process_emails()